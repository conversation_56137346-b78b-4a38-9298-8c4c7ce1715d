using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace HolyBless.ProviderSecrets
{
    public interface IReadOnlyProviderSecretAppService : IApplicationService
    {
        Task<List<ProviderSecretDto>> GetSecretsByProviderIdAsync(int storageProviderId);
        Task<ProviderSecretDto> GetActiveSecretByProviderIdAsync(int storageProviderId);
    }
}
