using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;
using Volo.Abp.Application.Dtos;
using HolyBless.Buckets.Dtos;

namespace HolyBless.Buckets
{
    public interface IStorageBucketAppService :
        ICrudAppService< // Defines CRUD methods
            StorageBucketDto, // Used to show storage buckets
            int, // Primary key of the storage bucket entity
            PagedAndSortedResultRequestDto, // Used for paging/sorting
            CreateUpdateStorageBucketDto> // Used to create/update a storage bucket
    {
        Task<List<StorageBucketDto>> GetListByProviderIdAysnc(int providerId);
    }
}
