using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.ProviderSecrets
{
    public interface IProviderSecretAppService : IReadOnlyProviderSecretAppService,
        ICrudAppService< // Defines CRUD methods
            ProviderSecretDto, // Used to show provider secrets
            int, // Primary key of the provider secret entity
            PagedAndSortedResultRequestDto, // Used for paging/sorting
            CreateUpdateProviderSecretDto> // Used to create/update a provider secret
    {
        Task<List<ProviderSecretDto>> GetListByProviderIdAsync(int storageProviderId);
    }
}
