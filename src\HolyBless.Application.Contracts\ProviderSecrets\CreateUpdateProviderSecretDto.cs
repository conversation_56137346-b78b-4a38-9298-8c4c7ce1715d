using System.ComponentModel.DataAnnotations;

namespace HolyBless.ProviderSecrets
{
    public class CreateUpdateProviderSecretDto
    {
        [Required]
        public int StorageProviderId { get; set; }

        [Required]
        public string AccessId { get; set; } = default!;

        [Required]
        public string AccessSecretKey { get; set; } = default!;

        [Required]
        public string ApiEndPoint { get; set; } = default!;

        public string? Description { get; set; }
    }
}
