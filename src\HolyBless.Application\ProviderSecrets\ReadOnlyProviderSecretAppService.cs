using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Entities.Buckets;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.ProviderSecrets
{
    public class ReadOnlyProviderSecretAppService : ApplicationService, IReadOnlyProviderSecretAppService
    {
        protected readonly IRepository<ProviderSecret, int> _repository;
        protected readonly IRepository<StorageProvider, int> _storageProviderRepository;

        public ReadOnlyProviderSecretAppService(
            IRepository<ProviderSecret, int> repository,
            IRepository<StorageProvider, int> storageProviderRepository)
        {
            _repository = repository;
            _storageProviderRepository = storageProviderRepository;
        }

        public virtual async Task<List<ProviderSecretDto>> GetSecretsByProviderIdAsync(int storageProviderId)
        {
            var queryable = await _repository.GetQueryableAsync();
            var secrets = await queryable
                .Include(x => x.StorageProvider)
                .Where(x => x.StorageProviderId == storageProviderId)
                .ToListAsync();

            return ObjectMapper.Map<List<ProviderSecret>, List<ProviderSecretDto>>(secrets);
        }

        public virtual async Task<ProviderSecretDto> GetActiveSecretByProviderIdAsync(int storageProviderId)
        {
            var queryable = await _repository.GetQueryableAsync();
            var secret = await queryable
                .Include(x => x.StorageProvider)
                .Where(x => x.StorageProviderId == storageProviderId)
                .OrderByDescending(x => x.CreationTime)
                .FirstOrDefaultAsync();

            if (secret == null)
            {
                throw new EntityNotFoundException(typeof(ProviderSecret), $"StorageProviderId: {storageProviderId}");
            }

            return ObjectMapper.Map<ProviderSecret, ProviderSecretDto>(secret);
        }
    }
}
