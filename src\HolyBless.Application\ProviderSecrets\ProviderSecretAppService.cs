using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Entities.Buckets;
using HolyBless.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.ProviderSecrets
{
    [Authorize(HolyBlessPermissions.ProviderSecrets.Default)]
    [RemoteService(false)]
    public class ProviderSecretAppService : ReadOnlyProviderSecretAppService, IProviderSecretAppService
    {
        public ProviderSecretAppService(
            IRepository<ProviderSecret, int> repository,
            IRepository<StorageProvider, int> storageProviderRepository)
            : base(repository, storageProviderRepository)
        {
        }

        public async Task<ProviderSecretDto> GetAsync(int id)
        {
            var queryable = await _repository.GetQueryableAsync();
            var secret = await queryable
                .Include(x => x.StorageProvider)
                .FirstOrDefaultAsync(x => x.Id == id);

            Check.NotNull(secret, nameof(secret));
            return ObjectMapper.Map<ProviderSecret, ProviderSecretDto>(secret);
        }

        public async Task<PagedResultDto<ProviderSecretDto>> GetListAsync(PagedAndSortedResultRequestDto input)
        {
            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .Include(x => x.StorageProvider)
                .OrderBy(input.Sorting ?? "StorageProviderId")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);

            var secrets = await AsyncExecuter.ToListAsync(query);
            var totalCount = await AsyncExecuter.CountAsync(queryable);

            return new PagedResultDto<ProviderSecretDto>(
                totalCount,
                ObjectMapper.Map<List<ProviderSecret>, List<ProviderSecretDto>>(secrets)
            );
        }

        public async Task<List<ProviderSecretDto>> GetListByProviderIdAsync(int storageProviderId)
        {
            return await GetSecretsByProviderIdAsync(storageProviderId);
        }

        [Authorize(HolyBlessPermissions.ProviderSecrets.Create)]
        public async Task<ProviderSecretDto> CreateAsync(CreateUpdateProviderSecretDto input)
        {
            // Verify that the storage provider exists
            var storageProvider = await _storageProviderRepository.GetAsync(input.StorageProviderId);

            var secret = ObjectMapper.Map<CreateUpdateProviderSecretDto, ProviderSecret>(input);
            secret = await _repository.InsertAsync(secret, autoSave: true);

            return await GetAsync(secret.Id);
        }

        [Authorize(HolyBlessPermissions.ProviderSecrets.Edit)]
        public async Task<ProviderSecretDto> UpdateAsync(int id, CreateUpdateProviderSecretDto input)
        {
            var secret = await _repository.GetAsync(id);
            
            // Verify that the storage provider exists if it's being changed
            if (secret.StorageProviderId != input.StorageProviderId)
            {
                await _storageProviderRepository.GetAsync(input.StorageProviderId);
            }

            ObjectMapper.Map(input, secret);
            secret = await _repository.UpdateAsync(secret, autoSave: true);

            return await GetAsync(secret.Id);
        }

        [Authorize(HolyBlessPermissions.ProviderSecrets.Delete)]
        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id);
        }
    }
}
