using HolyBless.Buckets.Dtos;
using HolyBless.Configs;
using HolyBless.Entities.Buckets;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Buckets
{
    [RemoteService(false)]

    public class ReadOnlyStorageBucketAppService : HolyBlessAppService, IReadOnlyStorageBucketAppService
    {
        protected readonly IRepository<StorageBucket, int> _repository;
        protected readonly IMemoryCache _memoryCache;
        protected readonly AppConfig _settings;
        protected static readonly string KeyBucket = "storagebucket_cache";
        protected readonly MemoryCacheEntryOptions _memoryCacheEntryOptions = new()
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(600),
            SlidingExpiration = TimeSpan.FromMinutes(60),
            Priority = CacheItemPriority.High
        };

        public ReadOnlyStorageBucketAppService(
            IRepository<StorageBucket, int> repository,
            IMemoryCache memoryCache,
            AppConfig settings)
        {
            _repository = repository;
            _memoryCache = memoryCache;
            _settings = settings;
        }

        protected async Task<List<StorageBucket>> GetCachedAllBuckets()
        {
            if (!_memoryCache.TryGetValue(KeyBucket, out List<StorageBucket>? buckets))
            {
                var queryable = await _repository.GetQueryableAsync();
                var query = queryable
                    .Include(x => x.StorageProvider)
                    .Where(x => x.StorageProvider.Environment == _settings.Environment);
                    
                buckets = await AsyncExecuter.ToListAsync(query);
                
                if (buckets == null || buckets.Count == 0)
                {
                    throw new BusinessException("Could not find any storage buckets");
                }
                
                // Cache the result
                _memoryCache.Set(KeyBucket, buckets, _memoryCacheEntryOptions);
            }
            return buckets ?? [];
        }

        public async Task<List<StorageBucketDto>> GetBucketsByProviderIdAsync(int providerId)
        {
            var allBuckets = await GetCachedAllBuckets();
            var buckets = allBuckets.Where(x => x.StorageProviderId == providerId).ToList();
            
            var bucketDtos = ObjectMapper.Map<List<StorageBucket>, List<StorageBucketDto>>(buckets);
            
            // Set the StorageName for each bucket
            foreach (var bucketDto in bucketDtos)
            {
                var bucket = buckets.FirstOrDefault(x => x.Id == bucketDto.Id);
                if (bucket?.StorageProvider != null)
                {
                    bucketDto.StorageName = bucket.StorageProvider.ProviderName;
                }
            }
            
            return bucketDtos;
        }

        public async Task<List<StorageBucketDto>> GetBucketsByLanguageAsync(string languageCode)
        {
            Check.NotNullOrWhiteSpace(languageCode, nameof(languageCode));
            
            var allBuckets = await GetCachedAllBuckets();
            var buckets = allBuckets.Where(x => x.LanguageCode == languageCode).ToList();
            
            var bucketDtos = ObjectMapper.Map<List<StorageBucket>, List<StorageBucketDto>>(buckets);
            
            // Set the StorageName for each bucket
            foreach (var bucketDto in bucketDtos)
            {
                var bucket = buckets.FirstOrDefault(x => x.Id == bucketDto.Id);
                if (bucket?.StorageProvider != null)
                {
                    bucketDto.StorageName = bucket.StorageProvider.ProviderName;
                }
            }
            
            return bucketDtos;
        }

        public async Task<StorageBucketDto> GetBucketByNameAsync(string bucketName)
        {
            Check.NotNullOrWhiteSpace(bucketName, nameof(bucketName));
            
            var allBuckets = await GetCachedAllBuckets();
            var bucket = allBuckets.FirstOrDefault(x => x.BucketName == bucketName);
            
            if (bucket == null)
            {
                throw new BusinessException($"Could not find storage bucket with name: {bucketName}");
            }
            
            var bucketDto = ObjectMapper.Map<StorageBucket, StorageBucketDto>(bucket);
            
            if (bucket.StorageProvider != null)
            {
                bucketDto.StorageName = bucket.StorageProvider.ProviderName;
            }
            
            return bucketDto;
        }

        public async Task<List<StorageBucketDto>> GetAllBucketsAsync()
        {
            var allBuckets = await GetCachedAllBuckets();
            var bucketDtos = ObjectMapper.Map<List<StorageBucket>, List<StorageBucketDto>>(allBuckets);
            
            // Set the StorageName for each bucket
            foreach (var bucketDto in bucketDtos)
            {
                var bucket = allBuckets.FirstOrDefault(x => x.Id == bucketDto.Id);
                if (bucket?.StorageProvider != null)
                {
                    bucketDto.StorageName = bucket.StorageProvider.ProviderName;
                }
            }
            
            return bucketDtos;
        }
    }
}
