using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Entities.Buckets;
using HolyBless.Enums;
using Volo.Abp;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;

namespace HolyBless.SeedData
{
    //[DependsOn(typeof(TestStorageProviderDataSeeder))]
    public class TestBucketFileDataSeeder
        : IDataSeedContributor, ITransientDependency
    {
        private readonly IRepository<BucketFile, int> _bucketFileRepository;
        private readonly IRepository<StorageBucket, int> _bucketRepository;

        public TestBucketFileDataSeeder(
            IRepository<BucketFile, int> bucketFileRepository,
            IRepository<StorageBucket, int> bucketRepository
        )
        {
            _bucketFileRepository = bucketFileRepository;
            _bucketRepository = bucketRepository;
        }

        public async Task SeedAsync(DataSeedContext context)
        {
            if (await _bucketFileRepository.GetCountAsync() <= 0)
            {
                var bucket1 = await _bucketRepository.FirstOrDefaultAsync(b => b.BucketName == "Bucket1");
                var bucket2 = await _bucketRepository.FirstOrDefaultAsync(b => b.BucketName == "Bucket2");

                var file1 = await _bucketFileRepository.InsertAsync(
                    new BucketFile
                    {
                        FileName = "File1",
                        Title = "File 1 Title",
                        RelativePathInBucket = "/file1/path",
                        LanguageCode = "EN",
                        MediaType = MediaType.Image,
                        ContentCategory = ContentCategory.Thumbnail,
                        YoutubeId = "youtube-id-1",
                    },
                    autoSave: true
                );

                var file2 = await _bucketFileRepository.InsertAsync(
                     new BucketFile
                     {
                         FileName = "File2",
                         Title = "File 2 Title",
                         RelativePathInBucket = "/file2/path",
                         LanguageCode = "CN",
                         MediaType = MediaType.Video,
                         ContentCategory = ContentCategory.OriginalVideo,
                         YoutubeId = "youtube-id-2",
                     },
                     autoSave: true
                 );

                var file3 = await _bucketFileRepository.InsertAsync(
                    new BucketFile
                    {
                        FileName = "File3",
                        Title = "File 3 Title",
                        RelativePathInBucket = "/file3/path",
                        LanguageCode = "FR",
                        MediaType = MediaType.Audio,
                        ContentCategory = ContentCategory.OriginalAudio,
                        YoutubeId = "youtube-id-3",
                    },
                    autoSave: true
                );

                var file4 = await _bucketFileRepository.InsertAsync(
                    new BucketFile
                    {
                        FileName = "File4",
                        Title = "File 4 Title",
                        RelativePathInBucket = "/file4/path",
                        LanguageCode = "DE",
                        MediaType = MediaType.Document,
                        ContentCategory = ContentCategory.Document,
                        YoutubeId = "youtube-id-4",
                    },
                    autoSave: true
                );
                Check.NotNull(bucket1, nameof(StorageBucket));
            }
        }
    }
}