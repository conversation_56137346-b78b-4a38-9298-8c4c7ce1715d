﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Entities.Buckets;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using HolyBless.DataSeeders;
using HolyBless.Lookups;

namespace HolyBless.SeedData
{
    public class TestStorageProviderDataSeeder
        : IDataSeedContributor, ITransientDependency
    {
        private readonly IRepository<StorageProvider, int> _storageProviderRepository;
        private readonly IRepository<StorageBucket, int> _bucketRepository;

        public TestStorageProviderDataSeeder(
            IRepository<StorageProvider, int> storageProviderRepository
            , IRepository<StorageBucket, int> bucketRepository
            )
        {
            _storageProviderRepository = storageProviderRepository;
            _bucketRepository = bucketRepository;
        }

        public async Task SeedAsync(DataSeedContext context)
        {
            if (await _storageProviderRepository.GetCountAsync() <= 0)
            {
                var provider = await _storageProviderRepository.InsertAsync(
                    new StorageProvider
                    {
                        ProviderName = "CF",
                        PreferCountries = new List<Country> {
                            CountryDataSeederContributor.CountryList.First(c => c.Code == "US")
                        },
                        Environment = "Development"
                    },
                    autoSave: true
                );

                await _bucketRepository.InsertAsync(
                    new StorageBucket
                    {
                        BucketName = "Bucket1",
                        StorageProviderId = provider.Id,
                        LanguageCode = "US",
                        SpokenLangCode = "en",
                        SubDomain = "https://bucket1.com",
                    },
                    autoSave: true
                );

                provider = await _storageProviderRepository.InsertAsync(
                    new StorageProvider
                    {
                        ProviderName = "Ali",
                        PreferCountries = new List<Country> {
                            CountryDataSeederContributor.CountryList.First(c => c.Code == "CN")
                        },
                        Environment = "Prod"
                    },
                    autoSave: true
                );

                await _bucketRepository.InsertAsync(
                    new StorageBucket
                    {
                        BucketName = "Bucket2",
                        StorageProviderId = provider.Id,
                        LanguageCode = "CN",
                        SubDomain = "https://bucket2.com",
                    },
                    autoSave: true
                );
            }
        }
    }
}