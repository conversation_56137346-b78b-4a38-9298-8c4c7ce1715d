using System;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Buckets.Dtos;
using Shouldly;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace HolyBless.Buckets
{
    public abstract class StorageBucketAppService_Tests<TStartupModule> : HolyBlessApplicationTestBase<TStartupModule>
            where TStartupModule : IAbpModule
    {
        private readonly IStorageBucketAppService _storageBucketAppService;

        protected StorageBucketAppService_Tests()
        {
            _storageBucketAppService = GetRequiredService<IStorageBucketAppService>();
        }

        [Fact]
        public async Task Should_Get_List_Of_StorageBuckets()
        {
            // Act
            var result = await _storageBucketAppService.GetListAsync(new PagedAndSortedResultRequestDto());

            // Assert
            result.TotalCount.ShouldBeGreaterThan(0);
            result.Items.ShouldContain(b => b.BucketName == "Bucket1");

            var item = result.Items.FirstOrDefault(b => b.<PERSON>etName == "Bucket1");
            item.ShouldNotBeNull();
            item.StorageName.ShouldBe("CF");

            var readBucket = await _storageBucketAppService.GetAsync(item.Id);
            readBucket.ShouldNotBeNull();
        }

        [Fact]
        public async Task Should_Not_Get_List_Of_StorageBuckets_Production()
        {
            // Act
            var result = await _storageBucketAppService.GetListAsync(new PagedAndSortedResultRequestDto());

            // Assert
            // Shouldn't contains Storage Buckets from production provider
            result.Items.ShouldNotContain(b => b.BucketName == "Bucket2");
        }

        [Fact]
        public async Task Should_Create_A_New_StorageBucket()
        {
            // Act
            var providerId = await GetOneValidProviderId();
            var result = await _storageBucketAppService.CreateAsync(
                new CreateUpdateStorageBucketDto
                {
                    BucketName = "NewBucket",
                    StorageProviderId = providerId,
                    LanguageCode = "US",
                    SpokenLangCode = "en",
                    SubDomain = "https://bucketNew.com",
                }
            );

            // Assert
            result.Id.ShouldNotBe(0);
            result.BucketName.ShouldBe("NewBucket");
            result.SpokenLangCode.ShouldBe("en");
        }

        [Fact]
        public async Task Should_Not_Create_A_StorageBucket_Without_Name()
        {
            // Act & Assert
            await Assert.ThrowsAsync<AbpValidationException>(async () =>
            {
                await _storageBucketAppService.CreateAsync(
                    new CreateUpdateStorageBucketDto
                    {
                        BucketName = "",
                        StorageProviderId = 1000,
                        LanguageCode = "US",
                        SubDomain = "https://bucketNew.com",
                    }
                );
            });
        }

        [Fact]
        public async Task Should_Delete_Existing_StorageBucket()
        {
            var providerId = await GetOneValidProviderId();
            // Arrange
            var bucket = await _storageBucketAppService.CreateAsync(
                new CreateUpdateStorageBucketDto
                {
                    BucketName = "DeleteBucket",
                    StorageProviderId = providerId,
                    LanguageCode = "US",
                    SubDomain = "https://bucketDelete.com",
                }
            );

            // Act
            await _storageBucketAppService.DeleteAsync(bucket.Id);

            // Assert
            var result = await _storageBucketAppService.GetListAsync(new PagedAndSortedResultRequestDto());
            result.Items.ShouldNotContain(b => b.BucketName == "DeleteBucket");
        }

        [Fact]
        public async Task Should_Get_List_Of_StorageBuckets_By_ProviderId()
        {
            // Arrange
            var providerId = await GetOneValidProviderId();

            // Act
            var result = await _storageBucketAppService.GetListByProviderIdAysnc(providerId);

            // Assert
            result.ShouldNotBeNull();
            result.ShouldAllBe(b => b.StorageProviderId == providerId);
            result.Count.ShouldBeGreaterThan(0);
        }

        private async Task<int> GetOneValidProviderId()
        {
            var existings = await _storageBucketAppService.GetListAsync(new PagedAndSortedResultRequestDto());
            var providerId = existings.Items.FirstOrDefault()?.StorageProviderId;
            return providerId ?? 0;
        }
    }
}
