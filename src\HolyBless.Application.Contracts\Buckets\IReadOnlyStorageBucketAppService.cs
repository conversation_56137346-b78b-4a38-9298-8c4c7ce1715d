using System.Collections.Generic;
using System.Threading.Tasks;
using HolyBless.Buckets.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.Buckets
{
    public interface IReadOnlyStorageBucketAppService : IApplicationService
    {
        Task<List<StorageBucketDto>> GetBucketsByProviderIdAsync(int providerId);
        Task<List<StorageBucketDto>> GetBucketsByLanguageAsync(string languageCode);
        Task<StorageBucketDto> GetBucketByNameAsync(string bucketName);
        Task<List<StorageBucketDto>> GetAllBucketsAsync();
    }
}
