using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Domain.Entities.Auditing;

namespace HolyBless.Entities.Buckets
{
    public class ProviderSecret : FullAuditedAggregateRoot<int>
    {
        public string AccessId { get; set; } = default!;
        public string AccessSecretKey { get; set; } = default!;
        public string ApiEndPoint { get; set; } = default!;

        public string? Description { get; set; }

        public ProviderSecret()
        {
        }

        public ProviderSecret(int id) : base(id)
        {
        }
    }
}