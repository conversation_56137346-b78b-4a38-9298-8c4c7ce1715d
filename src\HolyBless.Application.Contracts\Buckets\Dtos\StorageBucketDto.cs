using HolyBless.Enums;
using Volo.Abp.Application.Dtos;

namespace HolyBless.Buckets.Dtos
{
    public class StorageBucketDto : EntityDto<int>
    {
        public int StorageProviderId { get; set; }
        public string StorageName { get; set; } = default!;
        public string BucketName { get; set; } = default!;
        public string LanguageCode { get; set; } = default!;
        public string? SpokenLangCode { get; set; }
        public string SubDomain { get; set; } = default!; //StorageBucket Sub Domain Url
        public ContentCategory ContentType { get; set; }
    }
}
