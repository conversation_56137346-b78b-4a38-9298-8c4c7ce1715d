using System;
using Volo.Abp.Application.Dtos;

namespace HolyBless.ProviderSecrets
{
    public class ProviderSecretDto : EntityDto<int>
    {
        public int StorageProviderId { get; set; }
        public string StorageProviderName { get; set; } = default!;
        public string AccessId { get; set; } = default!;
        public string AccessSecretKey { get; set; } = default!;
        public string ApiEndPoint { get; set; } = default!;
        public string? Description { get; set; }
    }
}
