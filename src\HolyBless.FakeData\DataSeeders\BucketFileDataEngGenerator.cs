using Bogus;
using HolyBless.Entities.Buckets;
using HolyBless.Enums;
using System;
using System.Collections.Generic;

namespace HolyBless.FakeData.DataSeeders
{
    public static class BucketFileDataEngGenerator
    {
        private static readonly Random _random = new Random();
        private static readonly MediaType[] _mediaTypes = (MediaType[])Enum.GetValues(typeof(MediaType));
        private static readonly ContentCategory[] _contentCategories = (ContentCategory[])Enum.GetValues(typeof(ContentCategory));

        public static IEnumerable<BucketFile> Generate(int count = 100)
        {
            var currentId = 1;
            var curId = 1;
            var faker = new Faker<BucketFile>("en")
                .RuleFor(f => f.Id, f => 2000 + currentId++)
                .RuleFor(f => f.LanguageCode, f => LangCode.English)
                .RuleFor(f => f.FileName, f => f.System.FileName())
                .RuleFor(f => f.Title, f => f.Lorem.Sentence(3))
                .RuleFor(f => f.RelativePathInBucket, "")
                .RuleFor(f => f.MediaType, f => _mediaTypes[_random.Next(_mediaTypes.Length)])
                .RuleFor(f => f.ContentCategory, f => _contentCategories[_random.Next(_contentCategories.Length)])
                .RuleFor(f => f.DeliveryDate, f => SharedDeliveryDates.GetDeliveryDate(curId++))
                .RuleFor(f => f.Views, f => f.Random.Int(0, 1000))
                .RuleFor(f => f.YoutubeId, f => "")
                .RuleFor(f => f.FolderToBucketFiles, f => new List<global::HolyBless.Entities.VirtualFolders.FolderToBucketFile>())
                .RuleFor(f => f.CollectionToFiles, f => new List<global::HolyBless.Entities.Collections.CollectionToFile>());

            return faker.Generate(count);
        }
    }
}
